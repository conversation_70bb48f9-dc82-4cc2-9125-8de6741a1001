import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Filter, ChevronRight, SortAsc, Zap, X } from 'lucide-react';

// Base types for the filter system
export type SortDirection = 'asc' | 'desc';

export interface FilterOption {
  value: string;
  label: string;
}

export interface FilterField {
  key: string;
  label: string;
  type: 'text' | 'select' | 'number' | 'checkbox' | 'range';
  placeholder?: string;
  options?: FilterOption[];
  min?: number;
  max?: number;
  icon?: React.ReactNode;
  color?: string;
  gridCols?: number; // For responsive layout
}

export interface SortOption {
  key: string;
  label: string;
}

export interface UnifiedFilterProps {
  // Filter state
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  
  // Filter configuration
  filterFields: FilterField[];
  sortOptions: SortOption[];
  
  // Filter values and handlers
  filterValues: Record<string, any>;
  onFilterChange: (key: string, value: any) => void;
  onClearFilters: () => void;
  
  // Sort configuration
  sortBy: string;
  sortDirection: SortDirection;
  onSortChange: (sortBy: string, direction: SortDirection) => void;
  
  // Display configuration
  resultCount: number;
  totalCount: number;
  itemLabel?: string; // e.g., "accessories", "swimsuits"
  
  // Theme and styling
  accentColor?: string;
  secondaryColor?: string;
  expandableStats?: boolean;
  isFilterExpanded?: boolean;
  setIsFilterExpanded?: (expanded: boolean) => void;
  
  // Custom content
  additionalFilters?: React.ReactNode;
  headerIcon?: React.ReactNode;
  
  // Layout options
  className?: string;
}

export const UnifiedFilter: React.FC<UnifiedFilterProps> = ({
  showFilters,
  setShowFilters,
  filterFields,
  sortOptions,
  filterValues,
  onFilterChange,
  onClearFilters,
  sortBy,
  sortDirection,
  onSortChange,
  resultCount,
  totalCount,
  itemLabel = "items",
  accentColor = "accent-cyan",
  secondaryColor = "accent-purple",
  expandableStats = false,
  isFilterExpanded = false,
  setIsFilterExpanded,
  additionalFilters,
  headerIcon,
  className = ""
}) => {
  // Split filter fields into main and expandable sections
  const mainFields = filterFields.filter(field => !field.key.startsWith('min') || !expandableStats);
  const expandableFields = expandableStats ? filterFields.filter(field => field.key.startsWith('min')) : [];

  const getStatColor = (statType: string) => {
    switch (statType.toLowerCase()) {
      case 'pow': return 'text-red-400';
      case 'tec': return 'text-blue-400';
      case 'stm': return 'text-green-400';
      case 'apl': return 'text-yellow-400';
      default: return `text-${accentColor}`;
    }
  };

  const handleSortClick = (key: string) => {
    if (sortBy === key) {
      onSortChange(key, sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      onSortChange(key, 'asc');
    }
  };

  const SortButton = ({ sortKey, children }: { sortKey: string; children: React.ReactNode }) => (
    <motion.button
      onClick={() => handleSortClick(sortKey)}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className={`px-3 py-2 rounded-lg text-xs font-medium transition-all flex items-center gap-1 ${
        sortBy === sortKey
          ? `bg-gradient-to-r from-${accentColor} to-${secondaryColor} text-white shadow-md`
          : `bg-dark-card/50 text-gray-400 hover:text-white hover:bg-${accentColor}/20 border border-dark-border/50`
      }`}
    >
      {children}
      {sortBy === sortKey && (
        <motion.div
          animate={{ rotate: sortDirection === 'desc' ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <SortAsc className="w-3 h-3" />
        </motion.div>
      )}
    </motion.button>
  );

  const renderFilterField = (field: FilterField) => {
    const commonInputClasses = `w-full bg-dark-primary/50 border border-dark-border rounded-xl px-3 py-2 text-sm focus:outline-none focus:border-${accentColor} transition-all`;

    switch (field.type) {
      case 'text':
        return (
          <input
            type="text"
            value={filterValues[field.key] || ''}
            onChange={(e) => onFilterChange(field.key, e.target.value)}
            className={commonInputClasses}
            placeholder={field.placeholder}
          />
        );

      case 'select':
        return (
          <select
            value={filterValues[field.key] || ''}
            onChange={(e) => onFilterChange(field.key, e.target.value)}
            className={commonInputClasses}
          >
            <option value="">{field.placeholder || `All ${field.label}`}</option>
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        );

      case 'number':
        return (
          <input
            type="number"
            value={filterValues[field.key] || ''}
            onChange={(e) => onFilterChange(field.key, e.target.value)}
            className={commonInputClasses}
            placeholder={field.placeholder || '0'}
            min={field.min}
            max={field.max}
          />
        );

      case 'checkbox':
        return (
          <label className="flex items-center space-x-3 p-3 bg-dark-primary/30 rounded-xl border border-dark-border/50 hover:border-accent-cyan/50 transition-all cursor-pointer">
            <input
              type="checkbox"
              checked={filterValues[field.key] || false}
              onChange={(e) => onFilterChange(field.key, e.target.checked)}
              className={`rounded border-dark-border text-${accentColor} focus:ring-${accentColor}/20`}
            />
            <span className="text-sm text-gray-300">{field.label}</span>
          </label>
        );

      case 'range':
        return (
          <div className="flex items-center space-x-2">
            <input
              type="number"
              value={filterValues[`${field.key}Min`] || ''}
              onChange={(e) => onFilterChange(`${field.key}Min`, e.target.value)}
              className={commonInputClasses}
              placeholder="Min"
              min={field.min}
            />
            <span className="text-gray-400">-</span>
            <input
              type="number"
              value={filterValues[`${field.key}Max`] || ''}
              onChange={(e) => onFilterChange(`${field.key}Max`, e.target.value)}
              className={commonInputClasses}
              placeholder="Max"
              max={field.max}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* Filter Toggle Button and Results Count */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`flex items-center justify-between mb-8 ${className}`}
      >
        <div className="flex items-center gap-6">
          {headerIcon && (
            <div className="flex items-center">
              {headerIcon}
            </div>
          )}
        </div>

        <div className="flex items-center gap-3">
          <motion.button
            onClick={() => setShowFilters(!showFilters)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`px-4 py-3 rounded-xl transition-all flex items-center gap-2 ${
              showFilters 
                ? `bg-gradient-to-r from-${accentColor} to-${secondaryColor} text-white shadow-lg` 
                : `bg-dark-card/70 border border-dark-border/50 text-gray-400 hover:text-white hover:bg-${accentColor}/20`
            }`}
          >
            <Filter className="w-4 h-4" />
            <span className="text-sm font-medium">Filters</span>
          </motion.button>

          <div className="text-sm text-gray-500 bg-dark-card/50 px-3 py-3 rounded-xl border border-dark-border/50">
            <span className={`text-${accentColor} font-medium`}>{resultCount}</span> found
          </div>
        </div>
      </motion.div>

      {/* Advanced Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -20 }}
            animate={{ opacity: 1, height: 'auto', y: 0 }}
            exit={{ opacity: 0, height: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="mb-8 overflow-hidden"
          >
            <div className="bg-dark-card/50 backdrop-blur-md border border-dark-border/50 rounded-2xl p-6">
              {/* Filter Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white flex items-center">
                  <Filter className={`w-5 h-5 mr-2 text-${accentColor}`} />
                  Advanced Filters
                </h3>
                {expandableStats && setIsFilterExpanded && (
                  <button
                    onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                    className={`text-sm text-${accentColor} hover:text-${secondaryColor} transition-colors flex items-center`}
                  >
                    {isFilterExpanded ? 'Show Less Stats' : 'Show Stat Filters'}
                    <motion.div
                      animate={{ rotate: isFilterExpanded ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                      className="ml-1"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </motion.div>
                  </button>
                )}
              </div>

              {/* Main Filter Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                {mainFields.map((field) => (
                  <div key={field.key} className={field.gridCols ? `col-span-${field.gridCols}` : ''}>
                    {field.type !== 'checkbox' && (
                      <label className={`block text-sm font-medium mb-2 flex items-center ${field.color || 'text-gray-300'}`}>
                        {field.icon}
                        {field.label}
                      </label>
                    )}
                    {renderFilterField(field)}
                  </div>
                ))}
              </div>

              {/* Additional Custom Filters */}
              {additionalFilters && (
                <div className="mb-6">
                  {additionalFilters}
                </div>
              )}

              {/* Expandable Stat Filters */}
              {expandableStats && (
                <AnimatePresence>
                  {isFilterExpanded && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="overflow-hidden"
                    >
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        {expandableFields.map((field) => (
                          <div key={field.key}>
                            <label className={`block text-sm font-medium mb-2 flex items-center ${getStatColor(field.key)}`}>
                              {field.icon || <Zap className="w-3 h-3 mr-1" />}
                              {field.label}
                            </label>
                            {renderFilterField(field)}
                          </div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              )}

              {/* Sort Options */}
              <div className="flex flex-wrap gap-3 mb-6">
                <span className="text-sm text-gray-400 flex items-center mr-2">
                  <SortAsc className="w-4 h-4 mr-1" />
                  Sort by:
                </span>
                {sortOptions.map((option) => (
                  <SortButton key={option.key} sortKey={option.key}>
                    {option.label}
                  </SortButton>
                ))}
              </div>

              {/* Filter Actions */}
              <div className="flex items-center justify-between">
                <motion.button
                  onClick={onClearFilters}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`bg-gradient-to-r from-${secondaryColor}/20 to-accent-pink/20 hover:from-${secondaryColor}/30 hover:to-accent-pink/30 text-accent-pink border border-accent-pink/30 rounded-xl px-6 py-2 text-sm font-medium transition-all flex items-center gap-2`}
                >
                  <X className="w-4 h-4" />
                  Clear All Filters
                </motion.button>
                <div className="text-sm text-gray-500">
                  <span className={`text-${accentColor} font-medium`}>{resultCount}</span> of {totalCount} {itemLabel}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default UnifiedFilter; 